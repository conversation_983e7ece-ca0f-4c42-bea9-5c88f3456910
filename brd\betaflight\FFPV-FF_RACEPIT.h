/*==============================================================================
Generated on: 2025-06-11 20:35:53.889691
Generated by: betaflight_target_converter.py
Source: https://github.com/betaflight/unified-targets
Board name: FF_RACEPIT
Manufacturer ID: FFPV

//copy this line to madflight.ino to use this flight controller
#define MF_BOARD "brd/betaflight/FFPV-FF_RACEPIT.h"

Note: madflight will pick the last sensor defined here, this might not be the sensor that is actually on the board. Comment the offending sensors out, or use madflight_config to override.
==============================================================================*/

#define MF_BOARD_NAME "BETAFLIGHT-FFPV-FF_RACEPIT"
#define MF_MCU_NAME "STM32F405"

const char madflight_board[] = R""(
imu_bus_type SPI
imu_gizmo MPU6000 // #define USE_GYRO_SPI_MPU6000
pin_led PB9 // resource LED 1 B09
// resource LED 2 B08
// resource BEEPER 1 C03
// set beeper_inversion = ON
// set beeper_od = OFF
// resource PINIO 1 C00
// resource PINIO 2 C08
// resource CAMERA_CONTROL 1 A10
pin_i2c2_scl PA8 // resource I2C_SCL 3 A08
pin_i2c2_sda PC9 // resource I2C_SDA 3 C09
// set i2c3_pullup = ON
pin_spi0_sclk PA5 // resource SPI_SCK 1 A05
pin_spi0_miso PA6 // resource SPI_MISO 1 A06
pin_spi0_mosi PA7 // resource SPI_MOSI 1 A07
pin_spi1_sclk PB13 // resource SPI_SCK 2 B13
pin_spi1_miso PB14 // resource SPI_MISO 2 B14
pin_spi1_mosi PB15 // resource SPI_MOSI 2 B15
pin_spi2_sclk PB3 // resource SPI_SCK 3 B03
pin_spi2_miso PB4 // resource SPI_MISO 3 B04
pin_spi2_mosi PB5 // resource SPI_MOSI 3 B05
pin_imu_cs PA4 // resource GYRO_CS 1 A04
pin_imu_int PC4 // resource GYRO_EXTI 1 C04
// set gyro_1_bustype = SPI
imu_spi_bus 0 // set gyro_1_spibus = 1
imu_align CW90FLIP // set gyro_1_sensor_align = CW90FLIP
// set flash_spi_bus = 3
// resource FLASH_CS 1 A15
// resource OSD_CS 1 B12
// set max7456_spi_bus = 2
pin_out0 PB0 // resource MOTOR 1 B00
pin_out1 PB1 // resource MOTOR 2 B01
pin_out2 PB11 // resource MOTOR 3 B11
pin_out3 PB10 // resource MOTOR 4 B10
// resource LED_STRIP 1 B06
// set dshot_burst = ON
// set motor_pwm_protocol = Dshot600
pin_ser0_tx PA9 // resource SERIAL_TX 1 A09
pin_ser0_rx PB7 // resource SERIAL_RX 1 B07
pin_ser1_tx PA2 // resource SERIAL_TX 2 A02
pin_ser1_rx PA3 // resource SERIAL_RX 2 A03
pin_ser2_tx PC10 // resource SERIAL_TX 3 C10
pin_ser2_rx PC11 // resource SERIAL_RX 3 C11
pin_ser2_inv PC15 // resource INVERTER 3 C15
pin_ser3_tx PA0 // resource SERIAL_TX 4 A00
pin_ser3_rx PA1 // resource SERIAL_RX 4 A01
pin_ser4_tx PC12 // resource SERIAL_TX 5 C12
pin_ser4_rx PD2 // resource SERIAL_RX 5 D02
pin_ser5_tx PC6 // resource SERIAL_TX 6 C06
pin_ser5_rx PC7 // resource SERIAL_RX 6 C07
pin_bat_v PC2 // resource ADC_BATT 1 C02
bat_gizmo ADC
pin_bat_i PC1 // resource ADC_CURR 1 C01
bat_gizmo ADC
// resource ESCSERIAL 1 B00
// set pinio_config = 1,129,1,1
// set pinio_box = 40,41,255,255
// set tlm_halfduplex = OFF
// set blackbox_device = SPIFLASH
// set adc_device = 2
// set battery_meter = ADC
// set current_meter = ADC
)""; //end of madflight_board


/*
#==============================================================================
# BetaFlight Source file
#==============================================================================
# Betaflight / STM32F405 (S405) 4.0.0 Mar  2 2019 / 07:01:01 (29db27584) MSP API: 1.4

#define USE_ACC
#define USE_ACC_SPI_MPU6000
#define USE_GYRO
#define USE_GYRO_SPI_MPU6000
#define USE_FLASH
#define USE_FLASH_M25P16
#define USE_MAX7456

board_name FF_RACEPIT
manufacturer_id FFPV

defaults nosave

# Basic I/O
resource LED 1 B09
resource LED 2 B08
resource BEEPER 1 C03
set beeper_inversion = ON
set beeper_od = OFF
resource PINIO 1 C00
resource PINIO 2 C08
resource CAMERA_CONTROL 1 A10

# Buses
resource I2C_SCL 3 A08
resource I2C_SDA 3 C09
set i2c3_pullup = ON

resource SPI_SCK 1 A05
resource SPI_MISO 1 A06
resource SPI_MOSI 1 A07

resource SPI_SCK 2 B13
resource SPI_MISO 2 B14
resource SPI_MOSI 2 B15

resource SPI_SCK 3 B03
resource SPI_MISO 3 B04
resource SPI_MOSI 3 B05

# Acc/gyro
resource GYRO_CS 1 A04
resource GYRO_EXTI 1 C04
set gyro_1_bustype = SPI
set gyro_1_spibus = 1
set gyro_1_sensor_align = CW90FLIP

# SPI Flash
set flash_spi_bus = 3
resource FLASH_CS 1 A15

# OSD
resource OSD_CS 1 B12
set max7456_spi_bus = 2

# Timers
timer B00 AF2
# pin B00: TIM3 CH3 (AF2)
timer B01 AF2
# pin B01: TIM3 CH4 (AF2)
timer B11 AF1
# pin B11: TIM2 CH4 (AF1)
timer B10 AF1
# pin B10: TIM2 CH3 (AF1)
timer A10 AF1
# pin A10: TIM1 CH3 (AF1)
timer B06 AF2
# pin B06: TIM4 CH1 (AF2)
resource MOTOR 1 B00
resource MOTOR 2 B01
resource MOTOR 3 B11
resource MOTOR 4 B10
resource LED_STRIP 1 B06

# DMA
dma ADC 2 1      # ADC 2:   DMA2 Stream 3 Channel 1
dma pin B06 0    # pin B06: DMA1 Stream 0 Channel 2 
dma pin B00 0    # pin B00: DMA1 Stream 7 Channel 5
dma pin B01 0    # pin B01: DMA1 Stream 2 Channel 5
dma pin B10 0    # pin B10: DMA1 Stream 1 Channel 3
dma pin B11 1    # pin B11: DMA1 Stream 7 Channel 3

set dshot_burst = ON
set motor_pwm_protocol = Dshot600

# Serial ports
resource SERIAL_TX 1 A09
resource SERIAL_RX 1 B07

resource SERIAL_TX 2 A02
resource SERIAL_RX 2 A03

resource SERIAL_TX 3 C10
resource SERIAL_RX 3 C11
resource INVERTER 3 C15

resource SERIAL_TX 4 A00
resource SERIAL_RX 4 A01

resource SERIAL_TX 5 C12
resource SERIAL_RX 5 D02

resource SERIAL_TX 6 C06
resource SERIAL_RX 6 C07

# ADC
resource ADC_BATT 1 C02
resource ADC_CURR 1 C01

# Remaining
resource ESCSERIAL 1 B00

# Some configs
feature RX_SERIAL
feature OSD
serial 0 0 115200 57600 0 115200
serial 1 0 115200 57600 0 115200
serial 2 64 115200 57600 0 115200
serial 3 0 115200 57600 0 115200
serial 4 0 115200 57600 0 115200
serial 5 0 115200 57600 0 115200
set pinio_config = 1,129,1,1
set pinio_box = 40,41,255,255
set tlm_halfduplex = OFF
set blackbox_device = SPIFLASH
set adc_device = 2
set battery_meter = ADC
set current_meter = ADC

*/
