// Simple test to check if basic libraries compile
#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <Wire.h>
#include <SPI.h>
#include <ESP32Servo.h>
#include <Adafruit_GFX.h>
#include <Adafruit_ST7735.h>
#include <SoftwareSerial.h>
#include <TinyGPS++.h>

void setup() {
  Serial.begin(115200);
  Serial.println("Test compilation successful!");
}

void loop() {
  delay(1000);
}
