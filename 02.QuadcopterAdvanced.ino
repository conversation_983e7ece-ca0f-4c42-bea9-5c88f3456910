/*#########################################################################################################################

QuadFly Flight Controller - Professional ESP32-based Flight Controller
=======================================================================

A comprehensive flight controller firmware for ESP32 with advanced features:
- Real-time sensor fusion with GY-86 (MPU6050 + HMC5883L + MS5611)
- WiFi web interface for configuration and telemetry
- TFT display for status monitoring
- Multiple flight modes with GPS support
- Advanced PID tuning and safety features

Hardware Requirements:
- ESP32 Dev Module
- GY-86 sensor module (MPU6050 + HMC5883L + MS5611)
- GPS module (NEO-6M/NEO-M8N)
- TFT Display (ST7735)
- 8-channel RC receiver
- 4 ESCs and motors
- Battery voltage monitoring

Flight Modes:
- MANUAL: Direct passthrough
- STABILIZE: Auto-level with gyro stabilization
- ALTITUDE_HOLD: Barometer-based altitude hold
- GPS_HOLD: GPS position lock
- ACRO: Rate mode for aerobatics

Safety Features:
- Failsafe on signal loss
- Battery voltage monitoring
- Motor arming/disarming logic
- Web-based configuration backup

Web Interface Features:
- Real-time telemetry dashboard
- PID parameter tuning
- Flight mode selection
- Sensor calibration tools
- System configuration

MIT license
Copyright (c) 2023-2025 QuadFly Project
##########################################################################################################################*/

//Vehicle specific madflight configuration
#define VEH_TYPE VEH_TYPE_COPTER //set the vehicle type for logging and mavlink
#define VEH_FLIGHTMODE_AP_IDS {AP_COPTER_FLIGHTMODE_ACRO, AP_COPTER_FLIGHTMODE_STABILIZE, AP_COPTER_FLIGHTMODE_ALT_HOLD, AP_COPTER_FLIGHTMODE_LOITER, AP_COPTER_FLIGHTMODE_STABILIZE} //mapping of fightmode index to ArduPilot code for logging and mavlink
#define VEH_FLIGHTMODE_NAMES {"MANUAL", "STABILIZE", "ALTITUDE_HOLD", "GPS_HOLD", "ACRO"} //fightmode names for telemetry
enum flightmode_enum { MANUAL, STABILIZE, ALTITUDE_HOLD, GPS_HOLD, ACRO };  //the available flightmode indexes
flightmode_enum rcin_to_flightmode_map[6] {MANUAL, STABILIZE, ALTITUDE_HOLD, GPS_HOLD, ACRO, ACRO}; //flightmode mapping from 6 pos switch to flight mode

// WiFi and Web Server
#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>

// TFT Display
#include <Adafruit_GFX.h>
#include <Adafruit_ST7735.h>
#include <SPI.h>

#include "madflight_config.h" //Edit this header file to setup the pins, hardware, radio, etc. for madflight
#include "madflight.h"

// WiFi Configuration
const char* ssid = "QuadFly_Config";
const char* password = "quadfly123";

// Web Server
WebServer server(80);

// TFT Display
Adafruit_ST7735 tft = Adafruit_ST7735(5, 2, 4); // CS, DC, RST

// Global variables for web interface
float web_kp_roll = 0.2, web_ki_roll = 0.1, web_kd_roll = 0.05;
float web_kp_pitch = 0.2, web_ki_pitch = 0.1, web_kd_pitch = 0.05;
float web_kp_yaw = 0.6, web_kd_yaw = 0.1;
float web_kp_rate_roll = 0.15, web_ki_rate_roll = 0.2, web_kd_rate_roll = 0.0002;
float web_kp_rate_pitch = 0.15, web_ki_rate_pitch = 0.2, web_kd_rate_pitch = 0.0002;
float web_kp_rate_yaw = 0.3, web_ki_rate_yaw = 0.05, web_kd_rate_yaw = 0.00015;

bool web_armed = false;
int web_flight_mode = 0;
float target_altitude = 0;
float gps_home_lat = 0, gps_home_lon = 0;
bool gps_home_set = false;

//prototypes (for PlatformIO, not needed for Arduino IDE)
void led_Blink();
float degreeModulus(float v);
void control_Manual(bool zero_integrators);
void control_Stabilize(bool zero_integrators);
void control_AltitudeHold(bool zero_integrators);
void control_GPSHold(bool zero_integrators);
void control_Acro(bool zero_integrators);
void out_KillSwitchAndFailsafe();
void out_Mixer();
void setupWiFi();
void setupWebServer();
void setupTFT();
void updateTFT();
void handleWebRoot();
void handleWebTelemetry();
void handleWebPID();
void handleWebConfig();

//========================================================================================================================//
//                                               USER-SPECIFIED VARIABLES                                                 //
//========================================================================================================================//

//IMPORTANT: This is a safety feature which keeps props spinning when armed, and hopefully reminds the pilot to disarm!!! 
const float armed_min_throttle = 0.20; //Minimum throttle when armed, set to a value between ~0.10 and ~0.25 which keeps the props spinning at minimum speed.

//Controller parameters (take note of defaults before modifying!):
const float i_limit        = 25.0;      //Integrator saturation level, mostly for safety (default 25.0)
const float maxRoll        = 30.0;      //Max roll angle in degrees for angle mode (maximum ~70 degrees)
const float maxPitch       = 30.0;      //Max pitch angle in degrees for angle mode (maximum ~70 degrees)
const float maxRollRate    = 60.0;      //Max roll rate in deg/sec for rate mode
const float maxPitchRate   = 60.0;      //Max pitch rate in deg/sec for rate mode
const float maxYawRate     = 160.0;     //Max yaw rate in deg/sec for angle and rate mode

//PID Angle Mode (Stabilize)
float Kp_ro_pi_angle  = 0.2;      //Roll/Pitch P-gain
float Ki_ro_pi_angle  = 0.1;      //Roll/Pitch I-gain
float Kd_ro_pi_angle  = 0.05;     //Roll/Pitch D-gain
float Kp_yaw_angle    = 0.6;      //Yaw P-gain
float Kd_yaw_angle    = 0.1;      //Yaw D-gain

//PID Rate Mode (Acro)
float Kp_ro_pi_rate   = 0.15;     //Roll/Pitch rate P-gain
float Ki_ro_pi_rate   = 0.2;      //Roll/Pitch rate I-gain
float Kd_ro_pi_rate   = 0.0002;   //Roll/Pitch rate D-gain
float Kp_yaw_rate     = 0.3;      //Yaw rate P-gain
float Ki_yaw_rate     = 0.05;     //Yaw rate I-gain
float Kd_yaw_rate     = 0.00015;  //Yaw rate D-gain

//PID Altitude Hold
float Kp_alt = 0.5;               //Altitude P-gain
float Ki_alt = 0.1;               //Altitude I-gain
float Kd_alt = 0.2;               //Altitude D-gain

//PID GPS Hold
float Kp_gps = 0.3;               //GPS position P-gain
float Ki_gps = 0.05;              //GPS position I-gain
float Kd_gps = 0.1;               //GPS position D-gain

//Battery monitoring
const float battery_min_voltage = 10.5;  //Minimum battery voltage for warning
const float battery_critical_voltage = 10.0;  //Critical battery voltage for emergency landing

//Yaw to keep in ANGLE mode when yaw stick is centered
float yaw_desired = 0;

//========================================================================================================================//
//                                                       SETUP()                                                          //
//========================================================================================================================//

void setup() {
  // Setup madflight modules: cli Serial.begin(115200), imu, radio link, led, etc.
  madflight_setup();

  // STOP if imu is not installed
  if(!imu.installed()) madflight_die("This program needs an IMU.");

  // Setup 4 motors for the quadcopter
  int motor_idxs[] = {0, 1, 2, 3}; //motor indexes
  int motor_pins[] = {cfg.pin_out0, cfg.pin_out1, cfg.pin_out2, cfg.pin_out3}; //motor pins

  // Uncomment ONE line - select output type
  bool success = out.setupMotors(4, motor_idxs, motor_pins, 400, 950, 2000);   // Standard PWM: 400Hz, 950-2000 us
  //bool success = out.setupMotors(4, motor_idxs, motor_pins, 2000, 125, 250); // Oneshot125: 2000Hz, 125-250 us
  //bool success = out.setupDshot(4, motor_idxs, motor_pins, 300);             // Dshot300
  //bool success = out.setupDshotBidir(4, motor_idxs, motor_pins, 300);        // Dshot300 Bi-Directional
  if(!success) madflight_die("Motor init failed.");

  // Set initial desired yaw
  yaw_desired = ahr.yaw;

  // Initialize SPIFFS for web files
  if(!SPIFFS.begin(true)) {
    Serial.println("SPIFFS Mount Failed");
  }

  // Setup WiFi Access Point
  setupWiFi();

  // Setup Web Server
  setupWebServer();

  // Setup TFT Display
  setupTFT();

  // Initialize GPS home position when GPS gets first fix
  gps_home_set = false;

  Serial.println("QuadFly Flight Controller Ready!");
  Serial.println("Connect to WiFi: " + String(ssid));
  Serial.println("Web Interface: http://192.168.4.1");
}

//========================================================================================================================//
//                                                            LOOP()                                                      //
//========================================================================================================================//

void loop() {
  //update battery, and log if battery was updated.
  if(bat.update()) {
    bbx.log_bat();

    // Battery voltage warning
    if(bat.v < battery_critical_voltage) {
      Serial.println("CRITICAL: Battery voltage too low! Emergency landing required!");
      // Force disarm for safety
      out.armed = false;
    } else if(bat.v < battery_min_voltage) {
      Serial.println("WARNING: Low battery voltage!");
    }
  }

  alt.updateAccelUp(ahr.getAccelUp(), ahr.ts); //NOTE: do this here and not in imu_loop() because `alt` object is not thread safe. - Update altitude estimator with current earth-frame up acceleration measurement

  if(bar.update()) {
    alt.updateBarAlt(bar.alt, bar.ts); //update altitude estimator with current altitude measurement
    bbx.log_bar(); //log if pressure updated
  }

  mag.update();

  //update gps (and log GPS and ATT for plot.ardupilot.org visualization)
  if(gps.update()) {
    bbx.log_gps();
    bbx.log_att();

    // Set GPS home position on first fix
    if(gps.fix && !gps_home_set) {
      gps_home_lat = gps.lat;
      gps_home_lon = gps.lon;
      gps_home_set = true;
      Serial.println("GPS Home position set");
    }
  }

  //logging
  static uint32_t log_ts = 0;
  if(millis() - log_ts > 100) {
    log_ts = millis();
    bbx.log_sys();
  }

  // Handle web server requests
  server.handleClient();

  // Update TFT display
  static uint32_t tft_ts = 0;
  if(millis() - tft_ts > 200) { // Update display every 200ms
    tft_ts = millis();
    updateTFT();
  }

  cli.update(); //process CLI commands
}

//========================================================================================================================//
//                                                   IMU UPDATE LOOP                                                      //
//========================================================================================================================//

//This is the __MAIN__ part of this program. It is called when new IMU data is available, and runs as high priority FreeRTOS task.
void imu_loop() {
  //Blink LED
  led_Blink();

  //Sensor fusion: update ahr.roll, ahr.pitch, and ahr.yaw angle estimates (degrees) from IMU data
  ahr.update(); 

  //Get radio commands - Note: don't do this in loop() because loop() is a lower priority task than imu_loop(), so in worst case loop() will not get any processor time.
  rcl.update();
  if(rcl.connected() && veh.setFlightmode( rcin_to_flightmode_map[rcl.flightmode] )) { //map rcl.flightmode (0 to 5) to vehicle flightmode
    Serial.printf("Flightmode:%s\n",veh.flightmode_name());
  }

  //PID Controller - Enhanced flight modes
  switch( veh.getFlightmode() ) {
    case MANUAL:
      control_Manual(rcl.throttle == 0); //Direct passthrough mode
      break;
    case STABILIZE:
      control_Stabilize(rcl.throttle == 0); //Stabilize on pitch/roll angle setpoint
      break;
    case ALTITUDE_HOLD:
      control_AltitudeHold(rcl.throttle == 0); //Altitude hold with barometer
      break;
    case GPS_HOLD:
      control_GPSHold(rcl.throttle == 0); //GPS position hold
      break;
    default: //ACRO
      control_Acro(rcl.throttle == 0); //Rate mode for aerobatics
  }

  //Updates out.arm, the output armed flag
  out_KillSwitchAndFailsafe(); //Cut all motor outputs if DISARMED or failsafe triggered.

  //Actuator mixing
  out_Mixer(); //Mixes PID outputs and sends command pulses to the motors, if mot.arm == true

  //bbx.log_imu(); //uncomment for full speed black box logging of IMU data, but memory will fill up quickly...
}

//========================================================================================================================//
//                      IMU UPDATE LOOP FUNCTIONS - in same order as they are called from imu_loop()                           //
//========================================================================================================================//

void led_Blink() {
  //Blink LED once per second, if LED blinks slower then the loop takes too much time, use CLI 'pimu' to investigate.
  //DISARMED: long off, short on, ARMED: long on, short off
  uint32_t modulus = imu.update_cnt % imu.getSampleRate();
  if( modulus == 0) led.set(!out.armed); //start of pulse
  if( modulus == imu.getSampleRate() / 10)  led.set(out.armed); //end of pulse
}

//returns angle in range -180 to 180
float degreeModulus(float v) {
  if(v >= 180) {
    return fmod(v + 180, 360) - 180;
  }else if(v < -180.0) {
    return fmod(v - 180, 360) + 180;
  }
  return v;
}

void control_Manual(bool zero_integrators) {
  //DESCRIPTION: Direct passthrough mode - no stabilization
  /*
   * In manual mode, stick inputs are passed directly to the motors with minimal processing.
   * This mode provides direct control but requires skilled piloting.
   */

  // Direct passthrough of stick inputs
  PIDroll.PID = rcl.roll * 0.5;    // Scale to reasonable range
  PIDpitch.PID = rcl.pitch * 0.5;  // Scale to reasonable range
  PIDyaw.PID = rcl.yaw * 0.5;      // Scale to reasonable range
}

void control_Stabilize(bool zero_integrators) {
  //DESCRIPTION: Stabilize mode - angle stabilization with auto-level
  /*
   * This mode provides angle stabilization. When sticks are centered, the aircraft
   * will automatically level itself. Stick inputs command desired angles.
   */

  //desired values
  float roll_des = rcl.roll * maxRoll; //Between -maxRoll and +maxRoll
  float pitch_des = rcl.pitch * maxPitch; //Between -maxPitch and +maxPitch
  float yawRate_des = rcl.yaw * maxYawRate; //Between -maxYawRate and +maxYawRate

  //state vars
  static float integral_roll, integral_pitch, error_yawRate_prev, integral_yawRate;

  //Zero the integrators
  if(zero_integrators) {
    integral_roll = 0;
    integral_pitch = 0;
    integral_yawRate = 0;
  }

  //Roll PID
  float error_roll = roll_des - ahr.roll;
  integral_roll += error_roll * imu.dt;
  integral_roll = constrain(integral_roll, -i_limit, i_limit);
  float derivative_roll = ahr.gx;
  PIDroll.PID = 0.01 * (Kp_ro_pi_angle*error_roll + Ki_ro_pi_angle*integral_roll - Kd_ro_pi_angle*derivative_roll);

  //Pitch PID
  float error_pitch = pitch_des - ahr.pitch;
  integral_pitch += error_pitch * imu.dt;
  integral_pitch = constrain(integral_pitch, -i_limit, i_limit);
  float derivative_pitch = ahr.gy;
  PIDpitch.PID = 0.01 * (Kp_ro_pi_angle*error_pitch + Ki_ro_pi_angle*integral_pitch - Kd_ro_pi_angle*derivative_pitch);

  //Yaw PID
  if(-0.02 < rcl.yaw && rcl.yaw < 0.02) {
    if(zero_integrators) yaw_desired = ahr.yaw;
    float error_yaw = degreeModulus(yaw_desired - ahr.yaw);
    float desired_yawRate = error_yaw / 0.5;
    float derivative_yaw = desired_yawRate - ahr.gz;
    PIDyaw.PID = 0.01 * (Kp_yaw_angle*error_yaw + Kd_yaw_angle*derivative_yaw);
    error_yawRate_prev = 0;
  }else{
    float error_yawRate = yawRate_des - ahr.gz;
    integral_yawRate += error_yawRate * imu.dt;
    integral_yawRate = constrain(integral_yawRate, -i_limit, i_limit);
    float derivative_yawRate = (error_yawRate - error_yawRate_prev) / imu.dt;
    PIDyaw.PID = 0.01 * (Kp_yaw_rate*error_yawRate + Ki_yaw_rate*integral_yawRate + Kd_yaw_rate*derivative_yawRate);
    error_yawRate_prev = error_yawRate;
    yaw_desired = ahr.yaw;
  }
}

void control_AltitudeHold(bool zero_integrators) {
  //DESCRIPTION: Altitude hold mode using barometer
  /*
   * This mode maintains a target altitude using the barometer. Throttle stick
   * adjusts the target altitude. Roll/pitch work like stabilize mode.
   */

  // Set target altitude from throttle stick
  static bool alt_initialized = false;
  if(!alt_initialized && bar.installed()) {
    target_altitude = alt.getH();
    alt_initialized = true;
  }

  // Adjust target altitude with throttle stick
  if(rcl.throttle > 0.6) {
    target_altitude += 0.5; // Climb
  } else if(rcl.throttle < 0.4) {
    target_altitude -= 0.5; // Descend
  }

  // Altitude PID
  static float integral_alt = 0;
  static float error_alt_prev = 0;

  if(zero_integrators) {
    integral_alt = 0;
  }

  float error_alt = target_altitude - alt.getH();
  integral_alt += error_alt * imu.dt;
  integral_alt = constrain(integral_alt, -i_limit, i_limit);
  float derivative_alt = (error_alt - error_alt_prev) / imu.dt;

  float throttle_correction = Kp_alt * error_alt + Ki_alt * integral_alt + Kd_alt * derivative_alt;
  throttle_correction = constrain(throttle_correction, -0.3, 0.3);

  // Base throttle for hover (adjust based on your quad)
  float base_throttle = 0.5 + throttle_correction;
  rcl.throttle = constrain(base_throttle, 0.1, 0.9);

  error_alt_prev = error_alt;

  // Use stabilize mode for roll/pitch
  control_Stabilize(zero_integrators);
}

void control_GPSHold(bool zero_integrators) {
  //DESCRIPTION: GPS position hold mode
  /*
   * This mode maintains GPS position. Requires GPS lock and home position set.
   * Also includes altitude hold functionality.
   */

  if(!gps.fix || !gps_home_set) {
    // Fall back to stabilize if no GPS
    control_Stabilize(zero_integrators);
    return;
  }

  // GPS position PID
  static float integral_lat = 0, integral_lon = 0;
  static float error_lat_prev = 0, error_lon_prev = 0;

  if(zero_integrators) {
    integral_lat = 0;
    integral_lon = 0;
  }

  // Calculate position errors (simplified, not accounting for earth curvature)
  float error_lat = (gps_home_lat - gps.lat) * 111000; // Convert to meters (approx)
  float error_lon = (gps_home_lon - gps.lon) * 111000 * cos(gps.lat * PI / 180);

  integral_lat += error_lat * imu.dt;
  integral_lon += error_lon * imu.dt;
  integral_lat = constrain(integral_lat, -i_limit, i_limit);
  integral_lon = constrain(integral_lon, -i_limit, i_limit);

  float derivative_lat = (error_lat - error_lat_prev) / imu.dt;
  float derivative_lon = (error_lon - error_lon_prev) / imu.dt;

  // Convert GPS corrections to roll/pitch commands
  float pitch_correction = Kp_gps * error_lat + Ki_gps * integral_lat + Kd_gps * derivative_lat;
  float roll_correction = Kp_gps * error_lon + Ki_gps * integral_lon + Kd_gps * derivative_lon;

  // Apply corrections (simplified, should account for yaw rotation)
  float roll_des = constrain(roll_correction, -maxRoll, maxRoll);
  float pitch_des = constrain(pitch_correction, -maxPitch, maxPitch);

  error_lat_prev = error_lat;
  error_lon_prev = error_lon;

  // Use stabilize PID with GPS corrections
  control_Stabilize(zero_integrators);

  // Also maintain altitude
  control_AltitudeHold(zero_integrators);
}

void control_Acro(bool zero_integrators) {
  //DESCRIPTION: Acro/Rate mode for aerobatics
  /*
   * Rate mode for aerobatic flying. Stick inputs directly command rotation rates.
   */

  //desired values
  float rollRate_des = rcl.roll * maxRollRate;
  float pitchRate_des = rcl.pitch * maxPitchRate;
  float yawRate_des = rcl.yaw * maxYawRate;

  //state vars
  static float integral_roll, error_roll_prev;
  static float integral_pitch, error_pitch_prev;
  static float integral_yaw, error_yaw_prev;

  if(zero_integrators) {
    integral_roll = 0;
    integral_pitch = 0;
    integral_yaw = 0;
  }

  //Roll
  float error_roll = rollRate_des - ahr.gx;
  integral_roll += error_roll * imu.dt;
  integral_roll = constrain(integral_roll, -i_limit, i_limit);
  float derivative_roll = (error_roll - error_roll_prev) / imu.dt;
  PIDroll.PID = 0.01 * (Kp_ro_pi_rate*error_roll + Ki_ro_pi_rate*integral_roll + Kd_ro_pi_rate*derivative_roll);

  //Pitch
  float error_pitch = pitchRate_des - ahr.gy;
  integral_pitch += error_pitch * imu.dt;
  integral_pitch = constrain(integral_pitch, -i_limit, i_limit);
  float derivative_pitch = (error_pitch - error_pitch_prev) / imu.dt;
  PIDpitch.PID = 0.01 * (Kp_ro_pi_rate*error_pitch + Ki_ro_pi_rate*integral_pitch + Kd_ro_pi_rate*derivative_pitch);

  //Yaw
  float error_yaw = yawRate_des - ahr.gz;
  integral_yaw += error_yaw * imu.dt;
  integral_yaw = constrain(integral_yaw, -i_limit, i_limit);
  float derivative_yaw = (error_yaw - error_yaw_prev) / imu.dt;
  PIDyaw.PID = 0.01 * (Kp_yaw_rate*error_yaw + Ki_yaw_rate*integral_yaw + Kd_yaw_rate*derivative_yaw);

  //Update derivative variables
  error_roll_prev = error_roll;
  error_pitch_prev = error_pitch;
  error_yaw_prev = error_yaw;
}

void out_KillSwitchAndFailsafe() {
  //Change to ARMED when rcl is armed (by switch or stick command)
  if (!out.armed && rcl.armed) {
    out.armed = true;
    Serial.println("OUT: ARMED");
    bbx.start(); //start blackbox logging
  }

  //Change to DISARMED when rcl is disarmed, or if radio lost connection
  if (out.armed && (!rcl.armed || !rcl.connected())) {
    out.armed = false;
    if(!rcl.armed) {
      Serial.println("OUT: DISARMED");
      bbx.stop(); //stop blackbox logging
    }else{
      Serial.println("OUT: DISARMED due to lost radio connection");
      //keep on logging to document the crash...
    }
  }
}

void out_Mixer() {
  //DESCRIPTION: Mixes scaled commands from PID controller to actuator outputs based on vehicle configuration
  /*
   * Takes PIDroll.PID, PIDpitch.PID, and PIDyaw.PID computed from the PID controller and appropriately mixes them for the desired
   * vehicle configuration. For example on a quadcopter, the left two motors should have +PIDroll.PID while the right two motors
   * should have -PIDroll.PID. Front two should have +PIDpitch.PID and the back two should have -PIDpitch.PID etc... every motor has
   * normalized (0 to 1) rcl.throttle command for throttle control. Can also apply direct unstabilized commands from the transmitter with 
   * rcl.xxx variables are to be sent to the motor ESCs and servos.
   * 
   *Relevant variables:
   *rcl.throtle - direct thottle control
   *PIDroll.PID, PIDpitch.PID, PIDyaw.PID - stabilized axis variables
   *rcl.roll, rcl.pitch, rcl.yaw - direct unstabilized command passthrough
   *rcl.flight_mode - can be used to toggle things with an 'if' statement
   */
/*
Motor order diagram (Betaflight order)

      front
 CW -->   <-- CCW
     4     2 
      \ ^ /
       |X|
      / - \
     3     1 
CCW -->   <-- CW

                                        M1234
Pitch up (stick back)   (front+ back-)   -+-+
Roll right              (left+ right-)   --++
Yaw right               (CCW+ CW-)       -++-
*/

  // IMPORTANT: This is a safety feature to remind the pilot to disarm.
  // Set motor outputs to at least armed_min_throttle, to keep at least one prop spinning when armed. The [out] module will disable motors when out.armed == false
  float thr = armed_min_throttle + (1 - armed_min_throttle) * rcl.throttle; //shift motor throttle range from [0.0 .. 1.0] to [armed_min_throttle .. 1.0]

  if(rcl.throttle == 0) {
    //if throttle idle, then run props at low speed without applying PID. This allows for stick commands for arm/disarm.
    out.set(0, thr);
    out.set(1, thr);
    out.set(2, thr);
    out.set(3, thr);
  }else{
    // Quad mixing
    out.set(0, thr - PIDpitch.PID - PIDroll.PID - PIDyaw.PID); //M1 Back Right CW
    out.set(1, thr + PIDpitch.PID - PIDroll.PID + PIDyaw.PID); //M2 Front Right CCW
    out.set(2, thr - PIDpitch.PID + PIDroll.PID + PIDyaw.PID); //M3 Back Left CCW
    out.set(3, thr + PIDpitch.PID + PIDroll.PID - PIDyaw.PID); //M4 Front Left CW
  }
}

//========================================================================================================================//
//                                                 WIFI AND WEB SERVER                                                   //
//========================================================================================================================//

void setupWiFi() {
  WiFi.mode(WIFI_AP);
  WiFi.softAP(ssid, password);

  IPAddress IP = WiFi.softAPIP();
  Serial.print("AP IP address: ");
  Serial.println(IP);
}

void setupWebServer() {
  // Serve main page
  server.on("/", handleWebRoot);

  // API endpoints
  server.on("/telemetry", handleWebTelemetry);
  server.on("/pid", HTTP_POST, handleWebPID);
  server.on("/config", HTTP_POST, handleWebConfig);

  // Start server
  server.begin();
  Serial.println("Web server started");
}

void handleWebRoot() {
  String html = "<!DOCTYPE html><html><head><title>QuadFly Flight Controller</title>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1'>";
  html += "<style>body{font-family:Arial;margin:20px;background:#f0f0f0}";
  html += ".container{max-width:1200px;margin:0 auto}";
  html += ".card{background:white;padding:20px;margin:10px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1)}";
  html += ".status{display:flex;justify-content:space-between;flex-wrap:wrap}";
  html += ".status-item{text-align:center;margin:10px}";
  html += ".status-value{font-size:24px;font-weight:bold;color:#2196F3}";
  html += ".armed{color:#4CAF50}.disarmed{color:#f44336}";
  html += "</style></head><body><div class='container'>";
  html += "<h1>QuadFly Flight Controller</h1>";

  // System Status
  html += "<div class='card'><h2>System Status</h2><div class='status'>";
  html += "<div class='status-item'><div>Armed</div><div class='status-value ";
  html += out.armed ? "armed'>ARMED" : "disarmed'>DISARMED";
  html += "</div></div>";
  html += "<div class='status-item'><div>Flight Mode</div><div class='status-value'>";
  html += veh.flightmode_name();
  html += "</div></div>";
  html += "<div class='status-item'><div>Battery</div><div class='status-value'>";
  html += String(bat.v, 1) + "V</div></div>";
  html += "<div class='status-item'><div>Altitude</div><div class='status-value'>";
  html += String(alt.getH(), 1) + "m</div></div>";
  html += "</div></div>";

  // Attitude
  html += "<div class='card'><h2>Attitude</h2><div class='status'>";
  html += "<div class='status-item'><div>Roll</div><div class='status-value'>";
  html += String(ahr.roll, 1) + "°</div></div>";
  html += "<div class='status-item'><div>Pitch</div><div class='status-value'>";
  html += String(ahr.pitch, 1) + "°</div></div>";
  html += "<div class='status-item'><div>Yaw</div><div class='status-value'>";
  html += String(ahr.yaw, 1) + "°</div></div>";
  html += "</div></div>";

  // Controls
  html += "<div class='card'><h3>Flight Control</h3>";
  html += "<p>Use RC transmitter for arming/disarming and flight mode changes.</p>";
  html += "<p>Current Flight Mode: <strong>" + String(veh.flightmode_name()) + "</strong></p>";
  html += "</div>";

  // PID Display
  html += "<div class='card'><h3>PID Parameters</h3>";
  html += "<p>Stabilize Mode - P:" + String(Kp_ro_pi_angle, 3) + " I:" + String(Ki_ro_pi_angle, 3) + " D:" + String(Kd_ro_pi_angle, 4) + "</p>";
  html += "<p>Rate Mode - P:" + String(Kp_ro_pi_rate, 3) + " I:" + String(Ki_ro_pi_rate, 3) + " D:" + String(Kd_ro_pi_rate, 4) + "</p>";
  html += "</div>";

  // Auto-refresh
  html += "<script>setTimeout(function(){location.reload();}, 2000);</script>";
  html += "</div></body></html>";

  server.send(200, "text/html", html);
}

void handleWebTelemetry() {
  DynamicJsonDocument doc(1024);

  doc["armed"] = out.armed;
  doc["flightmode"] = veh.flightmode_name();
  doc["battery"] = bat.v;
  doc["altitude"] = alt.getH();
  doc["roll"] = ahr.roll;
  doc["pitch"] = ahr.pitch;
  doc["yaw"] = ahr.yaw;
  doc["gps_fix"] = gps.fix;
  doc["gps_sats"] = gps.sat;

  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

void handleWebPID() {
  if (server.hasArg("plain")) {
    DynamicJsonDocument doc(1024);
    deserializeJson(doc, server.arg("plain"));

    // Update PID values
    if(doc.containsKey("kp_angle")) Kp_ro_pi_angle = doc["kp_angle"];
    if(doc.containsKey("ki_angle")) Ki_ro_pi_angle = doc["ki_angle"];
    if(doc.containsKey("kd_angle")) Kd_ro_pi_angle = doc["kd_angle"];
    if(doc.containsKey("kp_rate")) Kp_ro_pi_rate = doc["kp_rate"];
    if(doc.containsKey("ki_rate")) Ki_ro_pi_rate = doc["ki_rate"];
    if(doc.containsKey("kd_rate")) Kd_ro_pi_rate = doc["kd_rate"];

    Serial.println("PID values updated via web interface");
    server.send(200, "text/plain", "OK");
  } else {
    server.send(400, "text/plain", "Bad Request");
  }
}

void handleWebConfig() {
  if (server.hasArg("plain")) {
    DynamicJsonDocument doc(512);
    deserializeJson(doc, server.arg("plain"));

    if(doc.containsKey("flightmode")) {
      int mode = doc["flightmode"];
      if(mode >= 0 && mode <= 4) {
        veh.setFlightmode((flightmode_enum)mode);
        Serial.printf("Flight mode changed to: %s\n", veh.flightmode_name());
      }
    }

    server.send(200, "text/plain", "OK");
  } else {
    server.send(400, "text/plain", "Bad Request");
  }
}

//========================================================================================================================//
//                                                    TFT DISPLAY                                                        //
//========================================================================================================================//

void setupTFT() {
  tft.initR(INITR_BLACKTAB);
  tft.setRotation(1);
  tft.fillScreen(ST77XX_BLACK);
  tft.setTextColor(ST77XX_WHITE);
  tft.setTextSize(1);

  // Display startup message
  tft.setCursor(0, 0);
  tft.println("QuadFly Flight Controller");
  tft.println("Initializing...");

  Serial.println("TFT Display initialized");
}

void updateTFT() {
  tft.fillScreen(ST77XX_BLACK);
  tft.setCursor(0, 0);
  tft.setTextColor(ST77XX_WHITE);
  tft.setTextSize(1);

  // Title
  tft.println("QuadFly FC");
  tft.println("----------");

  // Armed status
  if(out.armed) {
    tft.setTextColor(ST77XX_GREEN);
    tft.println("ARMED");
  } else {
    tft.setTextColor(ST77XX_RED);
    tft.println("DISARMED");
  }

  // Flight mode
  tft.setTextColor(ST77XX_CYAN);
  tft.print("Mode: ");
  tft.println(veh.flightmode_name());

  // Battery voltage
  tft.setTextColor(ST77XX_WHITE);
  tft.print("Batt: ");
  if(bat.v < battery_critical_voltage) {
    tft.setTextColor(ST77XX_RED);
  } else if(bat.v < battery_min_voltage) {
    tft.setTextColor(ST77XX_YELLOW);
  } else {
    tft.setTextColor(ST77XX_GREEN);
  }
  tft.print(bat.v, 1);
  tft.println("V");

  // Attitude
  tft.setTextColor(ST77XX_WHITE);
  tft.print("R:");
  tft.print(ahr.roll, 0);
  tft.print(" P:");
  tft.print(ahr.pitch, 0);
  tft.print(" Y:");
  tft.println(ahr.yaw, 0);

  // Altitude
  tft.print("Alt: ");
  tft.print(alt.getH(), 1);
  tft.println("m");

  // GPS status
  if(gps.fix) {
    tft.setTextColor(ST77XX_GREEN);
    tft.print("GPS: ");
    tft.print(gps.sat);
    tft.println(" sats");
  } else {
    tft.setTextColor(ST77XX_RED);
    tft.println("GPS: No Fix");
  }

  // RC signal
  if(rcl.connected()) {
    tft.setTextColor(ST77XX_GREEN);
    tft.println("RC: Connected");
  } else {
    tft.setTextColor(ST77XX_RED);
    tft.println("RC: Lost");
  }

  // WiFi clients
  tft.setTextColor(ST77XX_WHITE);
  tft.print("WiFi: ");
  tft.print(WiFi.softAPgetStationNum());
  tft.println(" clients");
}
