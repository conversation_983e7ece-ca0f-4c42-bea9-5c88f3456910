C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN16ImuGizmoICM426XX13getMotion6NEDEPfS0_S0_S0_S0_S0_[_ZN16ImuGizmoICM426XX13getMotion6NEDEPfS0_S0_S0_S0_S0_]+0x0): undefined reference to `ICM426XX::read(short*)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN16ImuGizmoICM4568613getMotion6NEDEPfS0_S0_S0_S0_S0_[_ZN16ImuGizmoICM4568613getMotion6NEDEPfS0_S0_S0_S0_S0_]+0x0): undefined reference to `ICM456xx::getDataFromFifo(inv_imu_fifo_data_t&)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13handleWebRootv+0x40): undefined reference to `out'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13handleWebRootv+0x4c): undefined reference to `veh'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13handleWebRootv+0x54): undefined reference to `bat'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13handleWebRootv+0x6c): undefined reference to `ahr'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13handleWebRootv+0xd0): undefined reference to `Veh::flightmode_name()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z9hal_setupv+0x0): undefined reference to `cfg'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN16ImuGizmoICM426XX6createEP9ImuConfigP8ImuState[_ZN16ImuGizmoICM426XX6createEP9ImuConfigP8ImuState]+0x14): undefined reference to `ICM426XX::detect(MPU_Interface*)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN16ImuGizmoICM426XX6createEP9ImuConfigP8ImuState[_ZN16ImuGizmoICM426XX6createEP9ImuConfigP8ImuState]+0x18): undefined reference to `ICM426XX::type_name()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN3Imu5setupEv+0x24): undefined reference to `CfgClass::printModule(char const*)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN3Imu5setupEv+0x28): undefined reference to `ICM456xx::ICM456xx(SPIClass&, unsigned char, unsigned long)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13madflight_die6String+0x0): undefined reference to `led'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13madflight_die6String+0xc): undefined reference to `cli'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13madflight_die6String+0x10): undefined reference to `rcl'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13madflight_die6String+0x18): undefined reference to `Led::toggle()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13madflight_die6String+0x1c): undefined reference to `Cli::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z13madflight_die6String+0x20): undefined reference to `Rcl::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x24): undefined reference to `bar'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x28): undefined reference to `mag'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x2c): undefined reference to `rdr'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x30): undefined reference to `gps'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x34): undefined reference to `bbx'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x3c): undefined reference to `cfg'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x40): undefined reference to `cfg'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x44): undefined reference to `cfg'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x5c): undefined reference to `CfgClass::begin()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x60): undefined reference to `CfgClass::loadFromEeprom()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x64): undefined reference to `CfgClass::load_madflight(char const*, char const*)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x68): undefined reference to `CfgClass::printPins()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x6c): undefined reference to `Led::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x70): undefined reference to `Led::on()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x74): undefined reference to `Cli::print_i2cScan()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x78): undefined reference to `CfgClass::getValue(String, float)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x7c): undefined reference to `Rcl::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x80): undefined reference to `Bar::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x84): undefined reference to `Mag::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x88): undefined reference to `Bat::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x8c): undefined reference to `Rdr::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x90): undefined reference to `Gps::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x94): undefined reference to `Bbx::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x98): undefined reference to `Ahr::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0x9c): undefined reference to `Ahr::setInitalOrientation()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0xa0): undefined reference to `Cli::calibrate_gyro()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0xa4): undefined reference to `Cli::begin()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z15madflight_setupv+0xa8): undefined reference to `Led::off()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z9led_Blinkv+0x0): undefined reference to `Led::set(bool)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z14control_Manualb+0x0): undefined reference to `PIDroll'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z14control_Manualb+0x4): undefined reference to `PIDpitch'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z14control_Manualb+0x8): undefined reference to `PIDyaw'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z25out_KillSwitchAndFailsafev+0xc): undefined reference to `Rcl::connected()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z25out_KillSwitchAndFailsafev+0x10): undefined reference to `Bbx::stop()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z25out_KillSwitchAndFailsafev+0x14): undefined reference to `Bbx::start()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z9out_Mixerv+0x8): undefined reference to `Out::set(unsigned char, float)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z8imu_loopv+0x8): undefined reference to `Ahr::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z8imu_loopv+0xc): undefined reference to `Veh::setFlightmode(unsigned char)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z8imu_loopv+0x10): undefined reference to `Veh::getFlightmode()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN16ImuGizmoICM456865beginEiii[_ZN16ImuGizmoICM456865beginEiii]+0x20): undefined reference to `ICM456xx::begin()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN16ImuGizmoICM456865beginEiii[_ZN16ImuGizmoICM456865beginEiii]+0x24): undefined reference to `ICM456xx::startAccel(unsigned short, unsigned short)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN16ImuGizmoICM456865beginEiii[_ZN16ImuGizmoICM456865beginEiii]+0x28): undefined reference to `ICM456xx::startGyro(unsigned short, unsigned short)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._ZN16ImuGizmoICM456865beginEiii[_ZN16ImuGizmoICM456865beginEiii]+0x2c): undefined reference to `ICM456xx::enableFifoInterrupt(unsigned char, void (*)(), unsigned char)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x14): undefined reference to `Bat::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x18): undefined reference to `Bbx::log_bat()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x1c): undefined reference to `Ahr::getAccelUp()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x20): undefined reference to `Bar::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x24): undefined reference to `Bbx::log_bar()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x28): undefined reference to `Mag::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x2c): undefined reference to `Gps::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x30): undefined reference to `Bbx::log_gps()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x34): undefined reference to `Bbx::log_att()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z4loopv+0x38): undefined reference to `Bbx::log_sys()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:(.literal._Z5setupv+0x24): undefined reference to `Out::setupMotors(unsigned char, int*, int*, int, int, int)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `ImuGizmoICM426XX::getMotion6NED(float*, float*, float*, float*, float*, float*)':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM426XX.h:66:(.text._ZN16ImuGizmoICM426XX13getMotion6NEDEPfS0_S0_S0_S0_S0_[_ZN16ImuGizmoICM426XX13getMotion6NEDEPfS0_S0_S0_S0_S0_]+0xe): undefined reference to `ICM426XX::read(short*)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `ImuGizmoICM45686::read6()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM45686.h:220:(.text._ZN16ImuGizmoICM4568613getMotion6NEDEPfS0_S0_S0_S0_S0_[_ZN16ImuGizmoICM4568613getMotion6NEDEPfS0_S0_S0_S0_S0_]+0xe): undefined reference to `ICM456xx::getDataFromFifo(inv_imu_fifo_data_t&)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `handleWebRoot()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:668:(.text._Z13handleWebRootv+0xe0): undefined reference to `Veh::flightmode_name()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:689:(.text._Z13handleWebRootv+0x2d8): undefined reference to `Veh::flightmode_name()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `ImuGizmoICM426XX::create(ImuConfig*, ImuState*)':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM426XX.h:26:(.text._ZN16ImuGizmoICM426XX6createEP9ImuConfigP8ImuState[_ZN16ImuGizmoICM426XX6createEP9ImuConfigP8ImuState]+0x67): undefined reference to `ICM426XX::detect(MPU_Interface*)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM426XX.h:39:(.text._ZN16ImuGizmoICM426XX6createEP9ImuConfigP8ImuState[_ZN16ImuGizmoICM426XX6createEP9ImuConfigP8ImuState]+0xa7): undefined reference to `ICM426XX::type_name()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `Imu::setup()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/imu_cpp.h:93:(.text._ZN3Imu5setupEv+0xb): undefined reference to `CfgClass::printModule(char const*)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `ImuGizmoICM45686::ImuGizmoICM45686(unsigned char, Invensensev3_InterfaceSPI*)':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM45686.h:127:(.text._ZN3Imu5setupEv+0x1d2): undefined reference to `ICM456xx::ICM456xx(SPIClass&, unsigned char, unsigned long)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `madflight_die(String)':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:278:(.text._Z13madflight_die6String+0x57): undefined reference to `Led::toggle()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:281:(.text._Z13madflight_die6String+0x7f): undefined reference to `Cli::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:282:(.text._Z13madflight_die6String+0x90): undefined reference to `Rcl::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `madflight_warn(String)':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:291:(.text._Z14madflight_warn6String+0x4f): undefined reference to `Led::toggle()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `madflight_setup()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:122:(.text._Z15madflight_setupv+0x82): undefined reference to `CfgClass::begin()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:128:(.text._Z15madflight_setupv+0x8a): undefined reference to `CfgClass::loadFromEeprom()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:129:(.text._Z15madflight_setupv+0x98): undefined reference to `CfgClass::load_madflight(char const*, char const*)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:136:(.text._Z15madflight_setupv+0xa0): undefined reference to `CfgClass::printPins()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:141:(.text._Z15madflight_setupv+0xb6): undefined reference to `Led::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:142:(.text._Z15madflight_setupv+0xbf): undefined reference to `Led::on()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:148:(.text._Z15madflight_setupv+0xd4): undefined reference to `Cli::print_i2cScan()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:154:(.text._Z15madflight_setupv+0x133): undefined reference to `CfgClass::getValue(String, float)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:155:(.text._Z15madflight_setupv+0x160): undefined reference to `Rcl::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:162:(.text._Z15madflight_setupv+0x187): undefined reference to `Bar::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:169:(.text._Z15madflight_setupv+0x1aa): undefined reference to `Mag::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:181:(.text._Z15madflight_setupv+0x1e2): undefined reference to `Bat::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:189:(.text._Z15madflight_setupv+0x207): undefined reference to `Rdr::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:195:(.text._Z15madflight_setupv+0x222): undefined reference to `Gps::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:204:(.text._Z15madflight_setupv+0x253): undefined reference to `Bbx::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:220:(.text._Z15madflight_setupv+0x2a3): undefined reference to `Ahr::setup()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:245:(.text._Z15madflight_setupv+0x3a6): undefined reference to `Ahr::setInitalOrientation()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:253:(.text._Z15madflight_setupv+0x3e7): undefined reference to `Cli::calibrate_gyro()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:261:(.text._Z15madflight_setupv+0x3f0): undefined reference to `Cli::begin()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/madflight.h:265:(.text._Z15madflight_setupv+0x402): undefined reference to `Led::off()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `led_Blink()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:319:(.text._Z9led_Blinkv+0x22): undefined reference to `Led::set(bool)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:320:(.text._Z9led_Blinkv+0x40): undefined reference to `Led::set(bool)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `out_KillSwitchAndFailsafe()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:558:(.text._Z25out_KillSwitchAndFailsafev+0x17): undefined reference to `Rcl::connected()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:562:(.text._Z25out_KillSwitchAndFailsafev+0x36): undefined reference to `Bbx::stop()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:553:(.text._Z25out_KillSwitchAndFailsafev+0x57): undefined reference to `Bbx::start()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `out_Mixer()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:609:(.text._Z9out_Mixerv+0x1e): undefined reference to `Out::set(unsigned char, float)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:610:(.text._Z9out_Mixerv+0x2b): undefined reference to `Out::set(unsigned char, float)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:611:(.text._Z9out_Mixerv+0x3a): undefined reference to `Out::set(unsigned char, float)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:618:(.text._Z9out_Mixerv+0x47): undefined reference to `Out::set(unsigned char, float)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:615:(.text._Z9out_Mixerv+0x87): undefined reference to `Out::set(unsigned char, float)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o:C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:616: more undefined references to `Out::set(unsigned char, float)' follow
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `imu_loop()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:276:(.text._Z8imu_loopv+0x9): undefined reference to `Ahr::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:279:(.text._Z8imu_loopv+0x15): undefined reference to `Rcl::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:280:(.text._Z8imu_loopv+0x1e): undefined reference to `Rcl::connected()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:280:(.text._Z8imu_loopv+0x37): undefined reference to `Veh::setFlightmode(unsigned char)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:281:(.text._Z8imu_loopv+0x41): undefined reference to `Veh::flightmode_name()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:281:(.text._Z8imu_loopv+0x55): undefined reference to `Veh::getFlightmode()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `updateTFT()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:802:(.text._Z9updateTFTv+0x58): undefined reference to `Veh::flightmode_name()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:828:(.text._Z9updateTFTv+0x162): undefined reference to `Rcl::connected()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `handleWebTelemetry()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:709:(.text._Z18handleWebTelemetryv+0x8b): undefined reference to `Veh::flightmode_name()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `handleWebConfig()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:751:(.text._Z15handleWebConfigv+0xf8): undefined reference to `Veh::setFlightmode(unsigned char)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:752:(.text._Z15handleWebConfigv+0x101): undefined reference to `Veh::flightmode_name()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `ImuGizmoICM45686::begin(int, int, int)':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM45686.h:152:(.text._ZN16ImuGizmoICM456865beginEiii[_ZN16ImuGizmoICM456865beginEiii]+0x1c): undefined reference to `ICM456xx::begin()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `ImuGizmoICM45686::_convertAccelScale(unsigned short)':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM45686.h:103:(.text._ZN16ImuGizmoICM456865beginEiii[_ZN16ImuGizmoICM456865beginEiii]+0x113): undefined reference to `ICM456xx::startAccel(unsigned short, unsigned short)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM45686.h:105:(.text._ZN16ImuGizmoICM456865beginEiii[_ZN16ImuGizmoICM456865beginEiii]+0x17b): undefined reference to `ICM456xx::startGyro(unsigned short, unsigned short)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `ImuGizmoICM45686::begin(int, int, int)':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM45686.h:176:(.text._ZN16ImuGizmoICM456865beginEiii[_ZN16ImuGizmoICM456865beginEiii]+0x192): undefined reference to `ICM456xx::enableFifoInterrupt(unsigned char, void (*)(), unsigned char)'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `ImuGizmoICM45686::_convertGyroScale(unsigned short)':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM45686.h:117:(.text._Z4loopv+0x9): undefined reference to `Bat::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced\imu/ImuGizmoICM45686.h:118:(.text._Z4loopv+0x15): undefined reference to `Bbx::log_bat()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `loop()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:223:(.text._Z4loopv+0x45): undefined reference to `Ahr::getAccelUp()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:225:(.text._Z4loopv+0x4e): undefined reference to `Bar::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:227:(.text._Z4loopv+0x5a): undefined reference to `Bbx::log_bar()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:230:(.text._Z4loopv+0x63): undefined reference to `Mag::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:233:(.text._Z4loopv+0x6e): undefined reference to `Gps::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:234:(.text._Z4loopv+0x7a): undefined reference to `Bbx::log_gps()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:235:(.text._Z4loopv+0x83): undefined reference to `Bbx::log_att()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:249:(.text._Z4loopv+0xd2): undefined reference to `Bbx::log_sys()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:258:(.text._Z4loopv+0xfb): undefined reference to `Cli::update()'
C:/Users/<USER>/AppData/Local/Arduino15/packages/esp32/tools/esp-x32/2411/bin/../lib/gcc/xtensa-esp-elf/14.2.0/../../../../xtensa-esp-elf/bin/ld.exe: C:\Users\<USER>\AppData\Local\arduino\sketches\10E623B2A6019D4F347806961A9FA5F5\sketch\02.QuadcopterAdvanced.ino.cpp.o: in function `setup()':
C:\Users\<USER>\Desktop\madflight-main\examples\02.QuadcopterAdvanced/02.QuadcopterAdvanced.ino:173:(.text._Z5setupv+0xac): undefined reference to `Out::setupMotors(unsigned char, int*, int*, int, int, int)'
collect2.exe: error: ld returned 1 exit status
exit status 1

Compilation error: exit status 1